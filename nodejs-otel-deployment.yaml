apiVersion: apps/v1
kind: Deployment
metadata:
  name: nodejs-otel-app
  labels:
    app: nodejs-otel-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nodejs-otel-app
  template:
    metadata:
      labels:
        app: nodejs-otel-app
    spec:
      containers:
      - name: nodejs-app
        image: node:18-alpine
        ports:
        - containerPort: 3000
        env:
        - name: NODE_OPTIONS
          value: "--require /app/node_modules/@opentelemetry/auto-instrumentations-node/build/src/register"
        - name: OTEL_SERVICE_NAME
          value: "nodejs-sample-app"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://coralogix-opentelemetry-collector.dev.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=nodejs-sample-app,service.version=1.0.0,deployment.environment=development"
        - name: OTE<PERSON>_TRACES_SAMPLER
          value: "always_on"
        - name: OTEL_LOG_LEVEL
          value: "debug"
        - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
          value: "http://coralogix-opentelemetry-collector.dev.svc.cluster.local:4317"
        workingDir: /app
        command: ["node", "server.js"]
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: app-code
          mountPath: /app
      initContainers:
      - name: install-otel
        image: node:18-alpine
        command:
        - sh
        - -c
        - |
          cd /app
          npm init -y
          npm install @opentelemetry/auto-instrumentations-node @opentelemetry/exporter-trace-otlp-proto @opentelemetry/exporter-trace-otlp-grpc
          npm install express
          cat > /app/server.js << 'EOF'
          const express = require('express');
          const { trace } = require('@opentelemetry/api');
          const app = express();
          const port = 3000;

          const tracer = trace.getTracer('nodejs-sample-app', '1.0.0');

          app.get('/', (req, res) => {
            const span = tracer.startSpan('home-request');
            span.setAttributes({
              'http.method': 'GET',
              'http.url': '/',
              'custom.attribute': 'home-page'
            });
            console.log('Processing home request with trace ID:', span.spanContext().traceId);
            res.send('Hello World from OpenTelemetry instrumented app!');
            span.end();
          });

          app.get('/api/users', (req, res) => {
            const span = tracer.startSpan('users-api-request');
            span.setAttributes({
              'http.method': 'GET',
              'http.url': '/api/users',
              'custom.attribute': 'users-api'
            });
            console.log('Processing users API request with trace ID:', span.spanContext().traceId);
            res.json([
              { id: 1, name: 'User 1' },
              { id: 2, name: 'User 2' }
            ]);
            span.end();
          });

          app.listen(port, () => {
            console.log(`App listening at http://localhost:${port}`);
            console.log('OpenTelemetry configuration:');
            console.log('- Service Name:', process.env.OTEL_SERVICE_NAME);
            console.log('- OTLP Endpoint:', process.env.OTEL_EXPORTER_OTLP_ENDPOINT);
            console.log('- Traces Endpoint:', process.env.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT);
            console.log('- Resource Attributes:', process.env.OTEL_RESOURCE_ATTRIBUTES);
          });
          EOF
        volumeMounts:
        - name: app-code
          mountPath: /app
      volumes:
      - name: app-code
        emptyDir: {}
