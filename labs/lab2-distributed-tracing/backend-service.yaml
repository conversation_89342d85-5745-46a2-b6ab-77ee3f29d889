apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-service
  labels:
    app: backend-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-service
  template:
    metadata:
      labels:
        app: backend-service
    spec:
      containers:
      - name: backend
        image: node:18-alpine
        ports:
        - containerPort: 3000
        env:
        - name: NODE_OPTIONS
          value: "--require @opentelemetry/auto-instrumentations-node/register"
        - name: OTEL_SERVICE_NAME
          value: "backend-service"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://coralogix-opentelemetry-collector.dev.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=backend-service,service.version=1.0.0,deployment.environment=lab"
        - name: OTEL_TRACES_SAMPLER
          value: "always_on"
        workingDir: /app
        command: ["node", "server.js"]
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: app-code
          mountPath: /app
      initContainers:
      - name: install-deps
        image: node:18-alpine
        command:
        - sh
        - -c
        - |
          cd /app
          npm init -y
          npm install express
          npm install @opentelemetry/auto-instrumentations-node @opentelemetry/exporter-trace-otlp-proto
          cat > /app/server.js << 'EOF'
          const express = require('express');
          const { trace } = require('@opentelemetry/api');
          const app = express();
          const port = 3000;

          const tracer = trace.getTracer('backend-service');

          // Mock database
          const products = [
            { id: 1, name: 'Product 1', price: 99.99 },
            { id: 2, name: 'Product 2', price: 149.99 },
            { id: 3, name: 'Product 3', price: 199.99 }
          ];

          app.get('/api/products', (req, res) => {
            const span = tracer.startSpan('get-products-db');
            try {
              console.log('Backend processing products request');
              span.setAttributes({
                'http.route': '/api/products',
                'service.component': 'backend',
                'db.operation': 'query',
                'db.collection': 'products'
              });
              
              // Simulate database query latency
              setTimeout(() => {
                res.json(products);
                span.end();
              }, 50);
            } catch (error) {
              console.error('Error fetching products:', error);
              span.recordException(error);
              span.setStatus({ code: 2, message: error.message }); // Error status
              span.end();
              res.status(500).json({ error: 'Database error' });
            }
          });

          app.get('/api/products/:id', (req, res) => {
            const span = tracer.startSpan('get-product-by-id');
            try {
              const id = parseInt(req.params.id);
              console.log(`Backend processing product request for ID: ${id}`);
              
              span.setAttributes({
                'http.route': '/api/products/:id',
                'service.component': 'backend',
                'db.operation': 'query',
                'db.collection': 'products',
                'product.id': id
              });
              
              // Simulate database query latency
              setTimeout(() => {
                const product = products.find(p => p.id === id);
                
                if (product) {
                  res.json(product);
                } else {
                  span.setAttributes({ 'error.type': 'not_found' });
                  res.status(404).json({ error: 'Product not found' });
                }
                span.end();
              }, 30);
            } catch (error) {
              console.error('Error fetching product:', error);
              span.recordException(error);
              span.setStatus({ code: 2, message: error.message }); // Error status
              span.end();
              res.status(500).json({ error: 'Database error' });
            }
          });

          app.get('/health', (req, res) => {
            res.json({ status: 'ok', service: 'backend' });
          });

          app.listen(port, () => {
            console.log(`Backend service listening at http://localhost:${port}`);
            console.log('OpenTelemetry configuration:');
            console.log('- Service Name:', process.env.OTEL_SERVICE_NAME);
            console.log('- OTLP Endpoint:', process.env.OTEL_EXPORTER_OTLP_ENDPOINT);
          });
          EOF
        volumeMounts:
        - name: app-code
          mountPath: /app
      volumes:
      - name: app-code
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  labels:
    app: backend-service
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: backend-service