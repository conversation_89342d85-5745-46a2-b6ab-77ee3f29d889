apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-service
  labels:
    app: frontend-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend-service
  template:
    metadata:
      labels:
        app: frontend-service
    spec:
      containers:
      - name: frontend
        image: node:18-alpine
        ports:
        - containerPort: 3000
        env:
        - name: NODE_OPTIONS
          value: "--require @opentelemetry/auto-instrumentations-node/register"
        - name: OTEL_SERVICE_NAME
          value: "frontend-service"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://coralogix-opentelemetry-collector.dev.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=frontend-service,service.version=1.0.0,deployment.environment=lab"
        - name: OTEL_TRACES_SAMPLER
          value: "always_on"
        - name: BACKEND_SERVICE_URL
          value: "http://backend-service"
        workingDir: /app
        command: ["node", "server.js"]
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: app-code
          mountPath: /app
      initContainers:
      - name: install-deps
        image: node:18-alpine
        command:
        - sh
        - -c
        - |
          cd /app
          npm init -y
          npm install express axios
          npm install @opentelemetry/auto-instrumentations-node @opentelemetry/exporter-trace-otlp-proto
          cat > /app/server.js << 'EOF'
          const express = require('express');
          const axios = require('axios');
          const { trace } = require('@opentelemetry/api');
          const app = express();
          const port = 3000;

          const tracer = trace.getTracer('frontend-service');
          const backendUrl = process.env.BACKEND_SERVICE_URL || 'http://localhost:3001';

          app.get('/', async (req, res) => {
            const span = tracer.startSpan('frontend-home');
            try {
              console.log('Processing frontend home request');
              span.setAttributes({
                'http.route': '/',
                'service.component': 'frontend'
              });
              
              // Call the backend health endpoint
              const response = await axios.get(`${backendUrl}/health`);
              
              res.send(`Frontend Service - Backend status: ${response.data.status}`);
            } catch (error) {
              console.error('Error calling backend:', error.message);
              span.recordException(error);
              span.setStatus({ code: 2, message: error.message }); // Error status
              res.status(500).send('Error connecting to backend service');
            } finally {
              span.end();
            }
          });

          app.get('/api/products', async (req, res) => {
            const span = tracer.startSpan('get-products');
            try {
              console.log('Processing products request');
              span.setAttributes({
                'http.route': '/api/products',
                'service.component': 'frontend'
              });
              
              // Call the backend products endpoint
              const response = await axios.get(`${backendUrl}/api/products`);
              
              // Add some frontend-specific data
              const enrichedProducts = response.data.map(product => ({
                ...product,
                frontendTimestamp: new Date().toISOString()
              }));
              
              res.json(enrichedProducts);
            } catch (error) {
              console.error('Error fetching products:', error.message);
              span.recordException(error);
              span.setStatus({ code: 2, message: error.message }); // Error status
              res.status(500).json({ error: 'Failed to fetch products' });
            } finally {
              span.end();
            }
          });

          app.get('/health', (req, res) => {
            res.json({ status: 'ok', service: 'frontend' });
          });

          app.listen(port, () => {
            console.log(`Frontend service listening at http://localhost:${port}`);
            console.log('Backend URL:', backendUrl);
            console.log('OpenTelemetry configuration:');
            console.log('- Service Name:', process.env.OTEL_SERVICE_NAME);
            console.log('- OTLP Endpoint:', process.env.OTEL_EXPORTER_OTLP_ENDPOINT);
          });
          EOF
        volumeMounts:
        - name: app-code
          mountPath: /app
      volumes:
      - name: app-code
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  labels:
    app: frontend-service
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: frontend-service