# Lab 2: Distributed Tracing Across Microservices

This lab demonstrates how to implement distributed tracing across multiple microservices using OpenTelemetry.

## Learning Objectives

- Understand how to propagate context between services
- Configure multiple services to send traces to a collector
- Visualize end-to-end traces across service boundaries
- Learn about Kubernetes Service discovery

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│  ┌─────────────────┐      ┌─────────────────┐              │
│  │                 │      │                 │              │
│  │  Frontend       │      │  Backend        │              │
│  │  Service        │──────►  Service        │              │
│  │                 │      │                 │              │
│  └─────────────────┘      └────────┬────────┘              │
│          │                          │                       │
│          │                          │                       │
│          │                          │                       │
│          │                          │                       │
│          │         ┌────────────────▼───────────────┐      │
│          │         │                                │      │
│          └─────────►  OpenTelemetry Collector       │      │
│                    │                                │      │
│                    └────────────────┬───────────────┘      │
│                                     │                       │
└─────────────────────────────────────┼───────────────────────┘
                                      │
                                      ▼
                                Jaeger Backend
```

## Implementation Steps

1. **Deploy the OpenTelemetry Collector and Jaeger**
   ```bash
   kubectl apply -f otel-collector.yaml
   kubectl apply -f jaeger.yaml
   ```

2. **Deploy the Backend Service**
   ```bash
   kubectl apply -f backend-service.yaml
   ```

3. **Deploy the Frontend Service**
   ```bash
   kubectl apply -f frontend-service.yaml
   ```

4. **Access the Frontend Service**
   ```bash
   kubectl port-forward svc/frontend-service 8080:80
   ```

5. **Generate some traffic**
   ```bash
   curl http://localhost:8080
   curl http://localhost:8080/api/products
   ```

6. **View traces in Jaeger**
   ```bash
   kubectl port-forward svc/jaeger-query 16686:16686
   ```
   Then open http://localhost:16686 in your browser

## Key Concepts

### Distributed Tracing

- **Context Propagation**: How trace context is passed between services
- **W3C Trace Context**: Standard format for propagating trace context
- **Span Links**: Connecting related spans across service boundaries
- **Service Graphs**: Visualizing service dependencies

### Kubernetes Networking

- **Service Discovery**: How services find each other in Kubernetes
- **DNS Resolution**: How service names are resolved to IP addresses
- **ClusterIP Services**: Internal-only services for inter-service communication

## Troubleshooting

- Check frontend logs: `kubectl logs -l app=frontend-service`
- Check backend logs: `kubectl logs -l app=backend-service`
- Verify service connectivity: `kubectl exec -it <frontend-pod> -- curl backend-service:3000/health`

## Next Steps

- Add more services to the chain
- Implement error handling and see how it affects traces
- Add custom attributes to spans for business context
- Implement sampling strategies for production use