# Lab 1: Basic K8s Deployment with OpenTelemetry Auto-Instrumentation

This lab demonstrates how to deploy a simple Node.js application to Kubernetes with OpenTelemetry auto-instrumentation.

## Learning Objectives

- Understand Kubernetes Deployments and Services
- Learn how to configure OpenTelemetry auto-instrumentation
- Explore environment variables for configuration
- Visualize traces from your application

## Architecture

```
┌─────────────────────────────────────┐
│                                     │
│  ┌─────────────────────────────┐    │
│  │                             │    │
│  │  Node.js Application Pod    │    │
│  │  ┌─────────────────────┐    │    │
│  │  │                     │    │    │
│  │  │  Application        │───────────► External Requests
│  │  │  + OpenTelemetry    │    │    │
│  │  │    SDK              │    │    │
│  │  │                     │    │    │
│  │  └─────────────────────┘    │    │
│  │             │               │    │
│  └─────────────┼───────────────┘    │
│                ▼                     │
│  ┌─────────────────────────────┐    │
│  │                             │    │
│  │  OpenTelemetry Collector    │    │
│  │                             │    │
│  └─────────────────────────────┘    │
│                │                     │
└────────────────┼─────────────────────┘
                 ▼
         Telemetry Backend
         (Jaeger, Zipkin, etc.)
```

## Implementation Steps

1. **Deploy the OpenTelemetry Collector**
   ```bash
   kubectl apply -f otel-collector.yaml
   ```

2. **Deploy the Node.js application**
   ```bash
   kubectl apply -f nodejs-app.yaml
   ```

3. **Access the application**
   ```bash
   kubectl port-forward svc/nodejs-app 8080:80
   ```

4. **Generate some traffic**
   ```bash
   curl http://localhost:8080
   curl http://localhost:8080/api/users
   ```

5. **View traces**
   ```bash
   kubectl port-forward svc/otel-collector 16686:16686
   ```
   Then open http://localhost:16686 in your browser (if using Jaeger)

## Key Concepts

### Kubernetes Resources

- **Deployment**: Manages the desired state of your application
- **Service**: Exposes your application to network traffic
- **ConfigMap**: Stores configuration data
- **Secret**: Stores sensitive data like API keys

### OpenTelemetry Concepts

- **Auto-instrumentation**: Automatically adds telemetry to your application
- **Collector**: Receives, processes, and exports telemetry data
- **Exporter**: Sends data to a backend system
- **Processor**: Modifies telemetry data before export

## Troubleshooting

- Check pod logs: `kubectl logs -l app=nodejs-app`
- Check collector logs: `kubectl logs -l app=otel-collector`
- Verify service endpoints: `kubectl get endpoints`

## Next Steps

- Try modifying the application to add custom spans
- Experiment with different OpenTelemetry exporters
- Add metrics collection to your application