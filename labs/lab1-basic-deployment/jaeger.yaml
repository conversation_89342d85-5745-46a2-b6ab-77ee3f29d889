apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  labels:
    app: jaeger
spec:
  selector:
    matchLabels:
      app: jaeger
  template:
    metadata:
      labels:
        app: jaeger
    spec:
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:latest
        ports:
        - containerPort: 16686  # UI
        - containerPort: 14250  # Model
        - containerPort: 14268  # Collector HTTP
        - containerPort: 14269  # Admin
        env:
        - name: COLLECTOR_OTLP_ENABLED
          value: "true"
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 256Mi
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-collector
  labels:
    app: jaeger
spec:
  ports:
  - name: jaeger-collector
    port: 14250
    protocol: TCP
    targetPort: 14250
  - name: jaeger-http
    port: 14268
    protocol: TCP
    targetPort: 14268
  selector:
    app: jaeger
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  labels:
    app: jaeger
spec:
  type: ClusterIP
  ports:
  - name: query-ui
    port: 16686
    protocol: TCP
    targetPort: 16686
  selector:
    app: jaeger