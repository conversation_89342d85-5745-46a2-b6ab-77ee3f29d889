apiVersion: apps/v1
kind: Deployment
metadata:
  name: nodejs-app
  labels:
    app: nodejs-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nodejs-app
  template:
    metadata:
      labels:
        app: nodejs-app
    spec:
      containers:
      - name: nodejs-app
        image: node:18-alpine
        ports:
        - containerPort: 3000
        env:
        - name: NODE_OPTIONS
          value: "--require @opentelemetry/auto-instrumentations-node/register"
        - name: OTEL_SERVICE_NAME
          value: "nodejs-demo-app"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://coralogix-opentelemetry-collector.dev.svc.cluster.local:4317"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=nodejs-demo-app,service.version=1.0.0,deployment.environment=lab"
        - name: OTEL_TRACES_SAMPLER
          value: "always_on"
        workingDir: /app
        command: ["node", "server.js"]
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: app-code
          mountPath: /app
      initContainers:
      - name: install-deps
        image: node:18-alpine
        command:
        - sh
        - -c
        - |
          cd /app
          npm init -y
          npm install express
          npm install @opentelemetry/auto-instrumentations-node @opentelemetry/exporter-trace-otlp-proto
          cat > /app/server.js << 'EOF'
          const express = require('express');
          const { trace } = require('@opentelemetry/api');
          const app = express();
          const port = 3000;

          const tracer = trace.getTracer('nodejs-demo-app');

          app.get('/', (req, res) => {
            const span = tracer.startSpan('home-request');
            try {
              console.log('Processing home request');
              span.setAttributes({
                'http.route': '/',
                'custom.attribute': 'home-page'
              });
              res.send('Hello from pagal instrumented app!');
            } finally {
              span.end();
            }
          });

          app.get('/api/users', (req, res) => {
            const span = tracer.startSpan('users-api-request');
            try {
              console.log('Processing users API request');
              span.setAttributes({
                'http.route': '/api/users',
                'custom.attribute': 'users-api'
              });
              
              // Simulate database query
              setTimeout(() => {
                res.json([
                  { id: 1, name: 'User 1' },
                  { id: 2, name: 'User 2' }
                ]);
              }, 100);
            } finally {
              span.end();
            }
          });

          app.listen(port, () => {
            console.log(`App listening at http://localhost:${port}`);
            console.log('OpenTelemetry configuration:');
            console.log('- Service Name:', process.env.OTEL_SERVICE_NAME);
            console.log('- OTLP Endpoint:', process.env.OTEL_EXPORTER_OTLP_ENDPOINT);
          });
          EOF
        volumeMounts:
        - name: app-code
          mountPath: /app
      volumes:
      - name: app-code
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: nodejs-app
  labels:
    app: nodejs-app
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: nodejs-app