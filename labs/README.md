# Kubernetes and OpenTelemetry Labs

This directory contains hands-on labs for learning Kubernetes concepts with a focus on OpenTelemetry instrumentation.

## Labs Overview

1. **Lab 1: Basic K8s Deployment with OpenTelemetry Auto-Instrumentation**
   - Deploy a simple application with OpenTelemetry auto-instrumentation
   - Learn about K8s Deployments, Services, and environment variables

2. **Lab 2: Distributed Tracing Across Microservices**
   - Deploy multiple interconnected services
   - Configure context propagation between services
   - Visualize end-to-end traces

3. **Lab 3: Resource Usage Monitoring with OpenTelemetry**
   - Deploy the OpenTelemetry Operator
   - Configure resource metrics collection
   - Set up HorizontalPodAutoscaler based on custom metrics

4. **Lab 4: Kubernetes Events and Logs Collection**
   - Capture K8s events as OpenTelemetry logs
   - Implement structured logging
   - Correlate logs with traces

5. **Lab 5: Advanced Deployment Patterns with Observability**
   - Implement blue/green deployment with OpenTelemetry
   - Monitor deployment health with metrics
   - Implement automatic rollback based on telemetry data

## Prerequisites

- Kubernetes cluster (minikube, kind, or cloud provider)
- kubectl CLI tool
- Helm (for some labs)
- Docker (for building custom images)