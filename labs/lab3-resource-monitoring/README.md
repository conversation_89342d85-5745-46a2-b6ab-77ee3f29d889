# Lab 3: Resource Usage Monitoring with OpenTelemetry

This lab demonstrates how to monitor Kubernetes resource usage with OpenTelemetry and implement autoscaling based on custom metrics.

## Learning Objectives

- Deploy the OpenTelemetry Operator for Kubernetes
- Configure resource metrics collection
- Set up HorizontalPodAutoscaler based on custom metrics
- Understand Kubernetes resource limits and requests
- Learn about Prometheus metrics format

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│  ┌─────────────────┐      ┌─────────────────┐              │
│  │                 │      │                 │              │
│  │  Load           │      │  Application    │              │
│  │  Generator      │──────►  Service        │              │
│  │                 │      │                 │              │
│  └─────────────────┘      └────────┬────────┘              │
│                                    │                       │
│                                    │                       │
│  ┌─────────────────┐      ┌────────▼────────┐              │
│  │                 │      │                 │              │
│  │  HPA            │◄─────┤  OpenTelemetry  │              │
│  │  Controller     │      │  Collector      │              │
│  │                 │      │                 │              │
│  └─────────────────┘      
</augment_code_snippet>