# Telegraf Configuration
[global_tags]
  # will tag all metrics with dc=us-east-1 and rack = "1a"
  dc = "ap-south-1"
  rack = "1a"

# Configuration for telegraf agent
[agent]
  interval = "20s"
  round_interval = true
  metric_batch_size = 1000
  metric_buffer_limit = 10000
  flush_interval = "20s"
  flush_jitter = "0s"
  precision = ""



[[outputs.opentelemetry]]
 service_address = "ingress.private.coralogix.in:443"
 #compression = "gzip"
 #tls_enable = false
 insecure_skip_verify = false
 compression = "gzip"
 [outputs.opentelemetry.coralogix]
 private_key = "cxtp_6GQFXPwAbN2SEEnCz710MtrUue9YgL"
 application = "development"
 subsystem = "website"

[[inputs.http_response]]
  ## Server address (default http://localhost)
  #urls = ["https://corpgiftcity.axisbank.com/pre-login", "https://maximus.axisbank.co.in/external/customer/login?product=four_wheeler", "https://neo.axisbank.com/login/customer","https://gtb1.axisbank.com/pre-login-interim"]
  urls = [ "https://api.connectedbanking.axisbank.com", "https://apollo.api.axisbank.com", "https://billpay.api.axisbank.com", "https://cagdynatrace.axisbank.com", "https://connectedbanking.axisbank.com", "https://digibillpay.axisbank.com", "https://Digiserv.axisbank.com", "https://digiservcug.axisbank.com", "https://digitalbanking.axisbank.com", "https://DISCF.axisbank.com", "https://ecrml.axisbank.co.in", "https://ecrmlive.axisbank.co.in", "https://ecrmmobi.axisbank.co.in", "https://engageprolive.axisbank.co.in", "https://FIP.axisbank.com", "https://FIU.axisbank.com", "https://fiuweb.axisbank.com", "https://gstnitr.axisbank.com", "https://gtb.axisbank.com", "https://gtb1.axisbank.com", "https://integrator-supernova.api.axisbank.com", "https://invoicing.axisbank.com", "https://ISCF.axisbank.com", "https://juno.api.axisbank.com", "https://leap.api.axisbank.com", "https://lendingplatform.axisbank.com", "https://marketplace.axisbank.com", "https://maximus.axisbank.co.in", "https://mercury.api.axisbank.com", "https://midas.api.axisbank.com", "https://mobiapp.axisbank.co.in", "https://newwaba.axisbank.com", "https://nucleus.api.axisbank.com", "https://oauth.api.axisbank.com", "https://olive.api.axisbank.com", "https://openbanking.axisbank.com", "https://openbanking2.axisbank.com", "https://openbanking3.axisbank.com", "https://openbanking4.axisbank.com", "https://outremit.axisbank.com", "https://paymentbus.api.axisbank.com", "https://snafdms.axisbank.com", "https://supernova.api.axisbank.com", "https://transitprepaid.axisbank.com", "https://turn.axisbank.com", "https://turn.axisbank.com", "https://vcipbus.api.axisbank.com", "https://Videokyc.axisbank.com", "https://vision-pis.api.axisbank.com", "https://vision.api.axisbank.com", "https://reports.gtb1.axisbank.com", "https://vision-bio.api.axisbank.com", "https://neo.axisbank.com", "https://neopay.axisbank.com", "https://videopd.axisbank.com", "https://new-grabdeals.api.axisbank.com", "https://axistrustee.in", "https://videobanking.axisbank.com", "https://turnvb.axisbank.com", "https://www.axispensionfund.com", "https://openbanking4s.axisbank.com", "https://dbat-signer.api.axisbank.com", "https://midas-integ.api.axisbank.com","https://secure2c.axisbank.com", "https://midas-renewal.api.axisbank.com", "https://sparsh.axisbank.com", "https://digitalaccount.axisbank.com", "https://newwabacug.axisbank.com", "https://digital-lead.api.axisbank.com", "https://moxochat.axisbank.com", "https://axisgo.api.axisbank.com", "https://pace.api.axisbank.com", "https://sameeksha.axisbank.com", "https://corpgiftcity.axisbank.com", "https://Inspec.api.axisbank.com", "https://kfs.axisbank.com", "https://newwabaflows.axisbank.com", "https://www.google.com", "https://www.checkpoint.com", "https://www.microsoft.com" ]
## Set response_timeout (default 5 seconds)
   response_timeout = "60s"

  ## Whether to follow redirects from the server (defaults to false)

# Reads metrics from a SSL certificate

  ## Whether to follow redirects from the server (defaults to false)

# Reads metrics from a SSL certificate
[[inputs.x509_cert]]
 ## List certificate sources, support wildcard expands for files
 ## Prefix your entry with 'file://' if you intend to use relative paths
 sources = [ "https://api.connectedbanking.axisbank.com", "https://apollo.api.axisbank.com", "https://billpay.api.axisbank.com", "https://cagdynatrace.axisbank.com", "https://connectedbanking.axisbank.com", "https://digibillpay.axisbank.com", "https://Digiserv.axisbank.com", "https://digiservcug.axisbank.com", "https://digitalbanking.axisbank.com", "https://DISCF.axisbank.com", "https://ecrml.axisbank.co.in", "https://ecrmlive.axisbank.co.in", "https://ecrmmobi.axisbank.co.in", "https://engageprolive.axisbank.co.in", "https://FIP.axisbank.com", "https://FIU.axisbank.com", "https://fiuweb.axisbank.com", "https://gstnitr.axisbank.com", "https://gtb.axisbank.com", "https://gtb1.axisbank.com", "https://integrator-supernova.api.axisbank.com", "https://invoicing.axisbank.com", "https://ISCF.axisbank.com", "https://juno.api.axisbank.com", "https://leap.api.axisbank.com", "https://lendingplatform.axisbank.com", "https://marketplace.axisbank.com", "https://maximus.axisbank.co.in", "https://mercury.api.axisbank.com", "https://midas.api.axisbank.com", "https://mobiapp.axisbank.co.in", "https://newwaba.axisbank.com", "https://nucleus.api.axisbank.com", "https://oauth.api.axisbank.com", "https://olive.api.axisbank.com", "https://openbanking.axisbank.com", "https://openbanking2.axisbank.com", "https://openbanking3.axisbank.com", "https://openbanking4.axisbank.com", "https://outremit.axisbank.com", "https://paymentbus.api.axisbank.com", "https://snafdms.axisbank.com", "https://supernova.api.axisbank.com", "https://transitprepaid.axisbank.com", "https://turn.axisbank.com", "https://turn.axisbank.com", "https://vcipbus.api.axisbank.com", "https://Videokyc.axisbank.com", "https://vision-pis.api.axisbank.com", "https://vision.api.axisbank.com", "https://reports.gtb1.axisbank.com", "https://vision-bio.api.axisbank.com", "https://neo.axisbank.com", "https://neopay.axisbank.com", "https://videopd.axisbank.com", "https://new-grabdeals.api.axisbank.com", "https://axistrustee.in", "https://videobanking.axisbank.com", "https://turnvb.axisbank.com", "https://www.axispensionfund.com", "https://openbanking4s.axisbank.com", "https://dbat-signer.api.axisbank.com", "https://midas-integ.api.axisbank.com", "https://secure2c.axisbank.com", "https://midas-renewal.api.axisbank.com", "https://sparsh.axisbank.com", "https://digitalaccount.axisbank.com", "https://newwabacug.axisbank.com", "https://digital-lead.api.axisbank.com", "https://moxochat.axisbank.com", "https://axisgo.api.axisbank.com", "https://pace.api.axisbank.com", "https://sameeksha.axisbank.com", "https://corpgiftcity.axisbank.com", "https://Inspec.api.axisbank.com", "https://kfs.axisbank.com", "https://newwabaflows.axisbank.com", "https://www.google.com", "https://www.checkpoint.com", "https://www.microsoft.com" ]
 ## Timeout for SSL connection
 timeout = "120s"

 ## Pass a different name into the TLS request (Server Name Indication).
 ## This is synonymous with tls_server_name, and only one of the two
 ## options may be specified at one time.

                                              

