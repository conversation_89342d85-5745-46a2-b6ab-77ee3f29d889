# Kubernetes Troubleshooting Scenarios

This directory contains practical troubleshooting scenarios for Kubernetes. Each scenario is designed to help you understand common issues that arise in Kubernetes environments and how to diagnose and resolve them.

## Scenarios Overview

1. **Scenario 1: Pod Startup Failures**
   - Troubleshoot container image issues
   - Debug init container failures
   - Resolve resource constraint problems

2. **Scenario 2: Service Discovery and Networking Issues**
   - Debug service selector mismatches
   - Troubleshoot DNS resolution problems
   - Diagnose network policy restrictions

3. **Scenario 3: Resource Constraints and Scheduling**
   - Investigate pod scheduling failures
   - Resolve resource quota limitations
   - Troubleshoot node affinity issues

4. **Scenario 4: ConfigMap and Secret Mounting Problems**
   - Debug volume mount issues
   - Resolve permission problems with mounted files
   - Troubleshoot dynamic updates of mounted ConfigMaps

5. **Scenario 5: Persistent Volume Claims Issues**
   - Diagnose PVC binding failures
   - Troubleshoot storage class problems
   - Resolve access mode conflicts

## How to Use These Scenarios

Each scenario folder contains:
- A detailed README with the problem description, symptoms, and step-by-step troubleshooting guide
- Kubernetes YAML files to reproduce the issue
- Solution files with corrected configurations

To use a scenario:

1. Create the scenario namespace:
   ```bash
   kubectl create ns troubleshooting-scenario-X
   ```

2. Apply the problematic configuration:
   ```bash
   kubectl apply -f scenario-X/problem/ -n troubleshooting-scenario-X
   ```

3. Follow the troubleshooting steps in the README

4. Verify your solution against the provided solution files:
   ```bash
   kubectl apply -f scenario-X/solution/ -n troubleshooting-scenario-X
   ```

## Prerequisites

- Kubernetes cluster (minikube, kind, or cloud provider)
- kubectl CLI tool
- Basic understanding of Kubernetes concepts