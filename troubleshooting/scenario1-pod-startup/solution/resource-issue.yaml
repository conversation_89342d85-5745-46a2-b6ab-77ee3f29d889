apiVersion: apps/v1
kind: Deployment
metadata:
  name: resource-issue-app
  labels:
    app: resource-issue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: resource-issue
  template:
    metadata:
      labels:
        app: resource-issue
    spec:
      containers:
      - name: nginx
        image: nginx:1.21.0
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: "500m"
            memory: "1Gi"
          requests:
            # Solution: Request reasonable resources
            cpu: "200m"
            memory: "512Mi"