apiVersion: apps/v1
kind: Deployment
metadata:
  name: init-container-issue
  labels:
    app: init-container-issue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: init-container-issue
  template:
    metadata:
      labels:
        app: init-container-issue
    spec:
      initContainers:
      - name: init-check
        image: busybox:1.28
        # Solution: Check for kubernetes.default service which always exists
        command: ['sh', '-c', 'until nslookup kubernetes.default; do echo waiting for service; sleep 2; done;']
      containers:
      - name: nginx
        image: nginx:1.21.0
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: "100m"
            memory: "128Mi"
          requests:
            cpu: "50m"
            memory: "64Mi"