apiVersion: apps/v1
kind: Deployment
metadata:
  name: resource-issue-app
  labels:
    app: resource-issue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: resource-issue
  template:
    metadata:
      labels:
        app: resource-issue
    spec:
      containers:
      - name: nginx
        image: nginx:1.21.0
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: "4000m"
            memory: "8Gi"
          requests:
            # Problem: Requesting more resources than available on most nodes
            cpu: "2000m"
            memory: "4Gi"