apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-issue-app
  labels:
    app: image-issue
spec:
  replicas: 1
  selector:
    matchLabels:
      app: image-issue
  template:
    metadata:
      labels:
        app: image-issue
    spec:
      containers:
      - name: nginx
        # Problem: This image tag doesn't exist
        image: nginx:1.99.99
        ports:
        - containerPort: 80
        resources:
          limits:
            cpu: "100m"
            memory: "128Mi"
          requests:
            cpu: "50m"
            memory: "64Mi"