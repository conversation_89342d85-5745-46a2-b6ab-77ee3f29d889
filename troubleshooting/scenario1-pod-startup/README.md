# Scenario 1: Pod Startup Failures

This scenario focuses on troubleshooting issues that prevent pods from starting successfully.

## Learning Objectives

- Diagnose container image pull failures
- Troubleshoot init container problems
- Identify and resolve resource constraint issues
- Understand pod lifecycle and startup sequence

## Setup Instructions

1. Create a dedicated namespace for this scenario:
   ```bash
   kubectl create ns troubleshooting-scenario1
   ```

2. Apply the problematic configurations:
   ```bash
   kubectl apply -f problem/ -n troubleshooting-scenario1
   ```

## Scenario Description

You've been asked to investigate several pods that are failing to start properly in a production environment. The development team has provided you with simplified versions of their deployments that exhibit the same issues.

## Problem 1: Image Pull Failure

### Symptoms
- Pod is stuck in `ImagePullBackOff` or `ErrImagePull` state
- Events show errors related to pulling the container image

### Troubleshooting Steps

1. Check the pod status:
   ```bash
   kubectl get pods -n troubleshooting-scenario1 -l app=image-issue
   ```

2. Examine the pod events:
   ```bash
   kubectl describe pod -n troubleshooting-scenario1 -l app=image-issue
   ```

3. Verify the image name and tag in the deployment:
   ```bash
   kubectl get deployment image-issue-app -n troubleshooting-scenario1 -o yaml
   ```

### Solution

The issue is caused by an incorrect image name or tag. The solution is to update the deployment with the correct image reference:

```bash
kubectl edit deployment image-issue-app -n troubleshooting-scenario1
```

Change the image from `nginx:1.99.99` to `nginx:1.21.0` (or another valid tag).

## Problem 2: Init Container Failure

### Symptoms
- Pod is stuck in `Init:CrashLoopBackOff` state
- Main container never starts
- Init container logs show errors

### Troubleshooting Steps

1. Check the pod status:
   ```bash
   kubectl get pods -n troubleshooting-scenario1 -l app=init-container-issue
   ```

2. Examine the pod events:
   ```bash
   kubectl describe pod -n troubleshooting-scenario1 -l app=init-container-issue
   ```

3. Check the init container logs:
   ```bash
   kubectl logs -n troubleshooting-scenario1 <pod-name> -c init-check
   ```

### Solution

The init container is failing because it's trying to check for a service that doesn't exist. The solution is to either:

1. Create the missing service:
   ```bash
   kubectl apply -f solution/missing-service.yaml -n troubleshooting-scenario1
   ```

2. Or modify the init container to check for an existing service:
   ```bash
   kubectl edit deployment init-container-issue -n troubleshooting-scenario1
   ```

## Problem 3: Resource Constraints

### Symptoms
- Pod is stuck in `Pending` state
- Events show messages about insufficient CPU or memory

### Troubleshooting Steps

1. Check the pod status:
   ```bash
   kubectl get pods -n troubleshooting-scenario1 -l app=resource-issue
   ```

2. Examine the pod events:
   ```bash
   kubectl describe pod -n troubleshooting-scenario1 -l app=resource-issue
   ```

3. Check the node resources:
   ```bash
   kubectl describe nodes
   ```

4. Check the resource requests in the deployment:
   ```bash
   kubectl get deployment resource-issue-app -n troubleshooting-scenario1 -o yaml
   ```

### Solution

The pod is requesting more resources than are available on any node. The solution is to reduce the resource requests:

```bash
kubectl edit deployment resource-issue-app -n troubleshooting-scenario1
```

Reduce the CPU request from `2000m` to `200m` and memory from `4Gi` to `512Mi`.

## Verification

After applying the solutions, verify that all pods are running:

```bash
kubectl get pods -n troubleshooting-scenario1
```

All pods should show `Running` status with all containers ready.

## Key Concepts

- **ImagePullBackOff**: Occurs when Kubernetes cannot pull the specified container image
- **Init Containers**: Run before the main container and must complete successfully
- **Resource Requests**: Specify the minimum amount of CPU and memory a pod needs
- **Pod Lifecycle**: The sequence of states a pod goes through from creation to termination