# Scenario 2: Service Discovery and Networking Issues

This scenario focuses on troubleshooting common networking and service discovery problems in Kubernetes.

## Learning Objectives

- Debug service selector mismatches
- Troubleshoot DNS resolution problems
- Diagnose network policy restrictions
- Understand Kubernetes service networking

## Setup Instructions

1. Create a dedicated namespace for this scenario:
   ```bash
   kubectl create ns troubleshooting-scenario2
   ```

2. Apply the problematic configurations:
   ```bash
   kubectl apply -f problem/ -n troubleshooting-scenario2
   ```

## Scenario Description

You're working with a microservices application where several services need to communicate with each other. The development team reports that some services cannot connect to others, and you need to investigate the networking issues.

## Problem 1: Service Selector Mismatch

### Symptoms
- Service endpoint shows no pods
- Pods cannot connect to the service
- DNS resolves but connection times out

### Troubleshooting Steps

1. Check the service and its endpoints:
   ```bash
   kubectl get svc frontend-service -n troubleshooting-scenario2
   kubectl get endpoints frontend-service -n troubleshooting-scenario2
   ```

2. Examine the service definition:
   ```bash
   kubectl describe svc frontend-service -n troubleshooting-scenario2
   ```

3. Check the pod labels:
   ```bash
   kubectl get pods -n troubleshooting-scenario2 -l app=frontend --show-labels
   ```

### Solution

The service selector doesn't match the pod labels. The solution is to update the service selector:

```bash
kubectl edit svc frontend-service -n troubleshooting-scenario2
```

Change the selector from `app: frontend-svc` to `app: frontend`.

## Problem 2: DNS Resolution Issues

### Symptoms
- Pods cannot resolve service names
- Error messages about unknown host
- nslookup or dig commands fail

### Troubleshooting Steps

1. Deploy a debug pod:
   ```bash
   kubectl apply -f debug-pod.yaml -n troubleshooting-scenario2
   ```

2. Test DNS resolution from the debug pod:
   ```bash
   kubectl exec -it debug-pod -n troubleshooting-scenario2 -- nslookup kubernetes.default
   kubectl exec -it debug-pod -n troubleshooting-scenario2 -- nslookup backend-service
   ```

3. Check the CoreDNS configuration:
   ```bash
   kubectl get configmap coredns -n kube-system -o yaml
   ```

4. Check if the DNS service is running:
   ```bash
   kubectl get pods -n kube-system -l k8s-app=kube-dns
   ```

### Solution

The issue is caused by incorrect DNS policy in the pod. The solution is to update the deployment:

```bash
kubectl edit deployment backend-app -n troubleshooting-scenario2
```

Change the DNS policy from `None` to `ClusterFirst`.

## Problem 3: Network Policy Restrictions

### Symptoms
- Pods can resolve service names but cannot connect
- Connection timeouts or refused errors
- Some pods can connect while others cannot

### Troubleshooting Steps

1. Check for network policies:
   ```bash
   kubectl get networkpolicies -n troubleshooting-scenario2
   ```

2. Examine the network policy details:
   ```bash
   kubectl describe networkpolicy restrict-database -n troubleshooting-scenario2
   ```

3. Test connectivity from different pods:
   ```bash
   kubectl exec -it debug-pod -n troubleshooting-scenario2 -- curl database-service:5432
   ```

### Solution

The network policy is too restrictive. The solution is to update the policy to allow traffic from the API service:

```bash
kubectl edit networkpolicy restrict-database -n troubleshooting-scenario2
```

Add a new ingress rule to allow traffic from pods with the label `app: api`.

## Verification

After applying the solutions, verify connectivity between services:

```bash
# Test frontend to backend connectivity
kubectl exec -it $(kubectl get pod -l app=frontend -n troubleshooting-scenario2 -o name | head -1) -n troubleshooting-scenario2 -- curl backend-service

# Test backend to database connectivity
kubectl exec -it $(kubectl get pod -l app=api -n troubleshooting-scenario2 -o name | head -1) -n troubleshooting-scenario2 -- curl database-service:5432
```

All connections should succeed without errors.

## Key Concepts

- **Service Selectors**: How Kubernetes services target specific pods
- **Endpoints**: The actual pod IP addresses that a service routes to
- **DNS Resolution**: How Kubernetes provides service discovery
- **Network Policies**: Kubernetes firewall rules that control pod-to-pod communication
- **DNS Policy**: How pods are configured to use DNS services