apiVersion: v1
kind: Service
metadata:
  name: backend-service
spec:
  selector:
    app: api
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
      - name: api
        image: nginx:1.21.0
        ports:
        - containerPort: 3000
        command: ["/bin/sh", "-c"]
        args:
          - |
            echo "Backend API running on port 3000" > /usr/share/nginx/html/index.html
            sed -i 's/listen       80/listen       3000/g' /etc/nginx/conf.d/default.conf
            nginx -g 'daemon off;'
      # Solution: Use ClusterFirst DNS policy
      dnsPolicy: ClusterFirst