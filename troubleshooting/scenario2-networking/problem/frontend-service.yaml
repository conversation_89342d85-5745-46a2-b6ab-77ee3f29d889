apiVersion: v1
kind: Service
metadata:
  name: frontend-service
spec:
  selector:
    # Problem: This selector doesn't match any pods
    app: frontend-svc
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: nginx:1.21.0
        ports:
        - containerPort: 8080
        command: ["/bin/sh", "-c"]
        args:
          - |
            echo "Frontend service running on port 8080" > /usr/share/nginx/html/index.html
            sed -i 's/listen       80/listen       8080/g' /etc/nginx/conf.d/default.conf
            nginx -g 'daemon off;'