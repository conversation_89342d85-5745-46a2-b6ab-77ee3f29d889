# Scenario 3: ConfigMap and Secret Mounting Problems

This scenario focuses on troubleshooting issues related to ConfigMaps and Secrets in Kubernetes.

## Learning Objectives

- Debug volume mount issues with ConfigMaps and Secrets
- Understand permission problems with mounted files
- Learn how to handle dynamic updates of mounted ConfigMaps
- Troubleshoot environment variable injection from ConfigMaps

## Setup Instructions

1. Create a dedicated namespace for this scenario:
   ```bash
   kubectl create ns troubleshooting-scenario3
   ```

2. Apply the problematic configurations:
   ```bash
   kubectl apply -f problem/ -n troubleshooting-scenario3
   ```

## Scenario Description

You're working with an application that uses ConfigMaps and Secrets for configuration. The development team reports several issues with accessing configuration data, and you need to investigate and fix these problems.

## Problem 1: ConfigMap Volume Mount Issues

### Symptoms
- Application logs show "file not found" errors
- Container cannot access configuration files
- Pod is running but application fails to start

### Troubleshooting Steps

1. Check the pod status:
   ```bash
   kubectl get pods -n troubleshooting-scenario3 -l app=config-app
   ```

2. Examine the pod logs:
   ```bash
   kubectl logs -n troubleshooting-scenario3 -l app=config-app
   ```

3. Check the ConfigMap:
   ```bash
   kubectl get configmap app-config -n troubleshooting-scenario3 -o yaml
   ```

4. Examine the pod configuration:
   ```bash
   kubectl describe pod -n troubleshooting-scenario3 -l app=config-app
   ```

### Solution

The ConfigMap is mounted at a different path than the application expects. The solution is to update the deployment:

```bash
kubectl edit deployment config-app -n troubleshooting-scenario3
```

Change the mount path from `/etc/configs` to `/app/config`.

## Problem 2: Secret Permission Issues

### Symptoms
- Application logs show permission denied errors
- Container cannot read secret files
- Pod is running but application fails to authenticate

### Troubleshooting Steps

1. Check the pod status:
   ```bash
   kubectl get pods -n troubleshooting-scenario3 -l app=secret-app
   ```

2. Examine the pod logs:
   ```bash
   kubectl logs -n troubleshooting-scenario3 -l app=secret-app
   ```

3. Check the Secret:
   ```bash
   kubectl get secret app-secret -n troubleshooting-scenario3 -o yaml
   ```

4. Examine the pod configuration:
   ```bash
   kubectl describe pod -n troubleshooting-scenario3 -l app=secret-app
   ```

5. Check file permissions inside the container:
   ```bash
   kubectl exec -it $(kubectl get pod -l app=secret-app -n troubleshooting-scenario3 -o name | head -1) -n troubleshooting-scenario3 -- ls -la /app/secrets
   ```

### Solution

The issue is caused by the security context of the container. The solution is to update the deployment:

```bash
kubectl edit deployment secret-app -n troubleshooting-scenario3
```

Remove the `readOnlyRootFilesystem: true` setting or add a proper `fsGroup` to the pod's security context.

## Problem 3: ConfigMap Update Issues

### Symptoms
- Application doesn't pick up ConfigMap changes
- Old configuration is still being used after updates
- No errors in logs, but behavior doesn't change

### Troubleshooting Steps

1. Check the current ConfigMap:
   ```bash
   kubectl get configmap dynamic-config -n troubleshooting-scenario3 -o yaml
   ```

2. Update the ConfigMap:
   ```bash
   kubectl edit configmap dynamic-config -n troubleshooting-scenario3
   ```

3. Check if the pod sees the changes:
   ```bash
   kubectl exec -it $(kubectl get pod -l app=dynamic-app -n troubleshooting-scenario3 -o name | head -1) -n troubleshooting-scenario3 -- cat /app/config/config.properties
   ```

4. Check the pod's configuration:
   ```bash
   kubectl describe pod -n troubleshooting-scenario3 -l app=dynamic-app
   ```

### Solution

The issue is that the pod is using the subPath mount option, which doesn't support dynamic updates. The solution is to update the deployment:

```bash
kubectl edit deployment dynamic-app -n troubleshooting-scenario3
```

Remove the `subPath: config.properties` setting from the volume mount.

## Verification

After applying the solutions, verify that all applications can access their configuration:

```bash
# Check if config-app can read its configuration
kubectl exec -it $(kubectl get pod -l app=config-app -n troubleshooting-scenario3 -o name | head -1) -n troubleshooting-scenario3 -- cat /app/config/app.properties

# Check if secret-app can read its secrets
kubectl exec -it $(kubectl get pod -l app=secret-app -n troubleshooting-scenario3 -o name | head -1) -n troubleshooting-scenario3 -- cat /app/secrets/api-key

# Check if dynamic-app picks up ConfigMap changes
kubectl edit configmap dynamic-config -n troubleshooting-scenario3  # Change a value
kubectl exec -it $(kubectl get pod -l app=dynamic-app -n troubleshooting-scenario3 -o name | head -1) -n troubleshooting-scenario3 