global:
  domain: "coralogix.in"
  clusterName: "kind-dev-cluster"
  collectionInterval: "60s"

opentelemetry-agent:
  enabled: true
  mode: daemonset
  presets:
    spanMetrics:
      enabled: true

    loadBalancing:
      enabled: true
      routingKey: "traceID"
      hostname: coralogix-opentelemetry-gateway
      # dnsResolverInterval: 20s
      # dnsResolverTimeout: 5s
  config:
    processors:
      attributes/conditional-drop:
        actions:
          # Drop 'caller' only if it matches "main.*"
          - key: "caller"
            pattern: "main.*"
            action: delete
          # Drop 'level' only if it matches "I" or "D"
          - key: "level"
            pattern: "^[ID]$"
            action: delete
          # Drop 'resourceSchemaUrl' only if it starts with "https"
          - key: "resourceSchemaUrl"
            pattern: "https.*"
            action: delete
          # Drop 'timestamp' only if it matches timestamp pattern
          - key: "timestamp"
            pattern: "^[0-9]{4}\\s[0-9]{2}:[0-9]{2}:[0-9]{2}.*"
            action: delete
          # Drop 'logtag' only if it's F, I, or D
          - key: "logtag"
            pattern: "^[FID]$"
            action: delete
          # Drop 'message' only if it contains "handling current node"
          - key: "message"
            pattern: ".*handling current node.*"
            action: delete

          
     
      filter/k8s_filter:
        logs:
          exclude:
            match_type: regexp
            resource_attributes:
              - key: k8s.namespace.name
                value: ".*linkerd.*|.*monitoring.*|.*opentelemetry.*|.*coralogix.*|.*twistlock.*|.*load-testing.*"
      
      transform/remove-loglabels:
        error_mode: ignore
        log_statements:
          - context: log
            statements:
              - delete_key(attributes, "host.image.id")
              - delete_key(attributes, "log.file.path")
              - delete_key(attributes, "log.iostream")
              - delete_key(attributes, "time")
              - delete_key(attributes, "event.domain")
              - delete_key(attributes, "event.name")
              - delete_key(attributes, "k8s.resource.name")
              - delete_key(resource.attributes, "k8s.pod.uid")
              - delete_key(resource.attributes, "cloud.account.id")
              - delete_key(resource.attributes, "cloud.platform")
              - delete_key(resource.attributes, "cloud.availability.zone")
              - delete_key(resource.attributes, "cloud.availability_zone")
              - delete_key(resource.attributes, "os.type")
              - delete_key(resource.attributes, "cloud.provider")
              - delete_key(resource.attributes, "cloud.region")
              - delete_key(resource.attributes, "host.type")
              - delete_key(resource.attributes, "cx.otel.integration.name")
              # - if body["caller"] matches "main.*" then delete_key(body, "caller")
              # - delete_key(log, "resourceSchemaUrl") where IsMatch(log.resourceSchemaUrl, "^https.*")
              # - delete_key(logRecord, "caller")
             
           
    service:
      pipelines:
        logs:
          processors:
            - k8sattributes
            - resourcedetection/env
            - resourcedetection/region
            - batch
            - attributes/conditional-drop
            - transform/remove-loglabels
            - filter/k8s_filter
        traces:
          exporters:
            - loadbalancing
opentelemetry-gateway:
  enabled: true
  # For production use-cases please increase replicas
  # and resource requests and limits
  replicaCount: 3
#    resources:
#      requests:
#        cpu: 0.5
#        memory: 256Mi
#      limits:
#        cpu: 2
#        memory: 2G
  config:
    processors:
      tail_sampling:
        # Update configuration here, with your settings and tail sampling policies
        # Docs: https://github.com/open-telemetry/opentelemetry-collector-contrib/tree/main/processor/tailsamplingprocessor
        policies:
          [
            {
              name: errors-policy,
              type: status_code,
              status_code: {status_codes: [ERROR]}
            },
            {
              name: randomized-policy,
              type: probabilistic,
              probabilistic: {sampling_percentage: 10}
            },
          ]
opentelemetry-cluster-collector:
  enabled: true
opentelemetry-agent-windows:
  enabled: false