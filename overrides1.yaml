global:
  domain: "coralogix.in"
  clusterName: "kind-dev-cluster"
  collectionInterval: "60s"

opentelemetry-agent:
  enabled: true
  mode: daemonset

  # ← THIS literal block becomes your agent’s config.yaml
  config: |-
    receivers:
      otlp:
        protocols:
          grpc: {}
          http: {}

    processors:
      # keep the built-in ones you need
      k8sattributes:
        filter:
          node_from_env_var: KUBE_NODE_NAME
        extract:
          metadata:
            - "k8s.namespace.name"
            - "k8s.pod.name"
      memory_limiter: null
      batch: {}

      # ← instantiate the built-in 'attributes' processor under your own name
      drop_attrs:
        attributes:
          actions:
            - action: delete
              key: caller
              match:
                match_type: regexp
                regexp: "^main.*"
            - action: delete
              key: resourceSchemaUrl
              match:
                match_type: regexp
                regexp: "^https.*"

      # example of using the transform processor (type 'transform') if you wanted CEL instead
      transform/remove-loglabels:
        error_mode: ignore
        log_statements:
          - context: log
            statements:
              - delete_key(attributes, "host.image.id")
              # … etc …

      filter/k8s_filter:
        logs:
          exclude:
            match_type: regexp
            resource_attributes:
              - key: k8s.namespace.name
                value: ".*(linkerd|monitoring|opentelemetry|coralogix|twistlock|load-testing).*"

    exporters:
      coralogix:
        private_key: "${CORALOGIX_PRIVATE_KEY}"
        domain:     "{{ .Values.global.domain }}"
        traces:
          endpoint: "{{ .Values.global.traces.endpoint }}"
        metrics:
          endpoint: "{{ .Values.global.metrics.endpoint }}"
        logs:
          endpoint: "{{ .Values.global.logs.endpoint }}"
        application_name_attributes:
          - "k8s.namespace.name"
        subsystem_name_attributes:
          - "k8s.deployment.name"

    service:
      pipelines:
        logs:
          receivers: [otlp]
          processors:
            - k8sattributes
            - batch
            - drop_attrs
            - transform/remove-loglabels
            - filter/k8s_filter
          exporters: [coralogix]
        traces:
          receivers: [otlp]
          processors:
            - memory_limiter
            - batch
            - drop_attrs
          exporters: [coralogix]
        metrics:
          receivers: [otlp]
          processors:
            - memory_limiter
            - batch
            - drop_attrs
          exporters: [coralogix]
